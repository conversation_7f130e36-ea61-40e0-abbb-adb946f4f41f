<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Our Club Midfielders - Poster</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Bebas+Neue&family=Montserrat:wght@400;600;700&display=swap" rel="stylesheet">

    <style>
        /* Custom Styles */
        body {
            font-family: 'Montserrat', sans-serif;
            background-color: #0f172a;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 1rem;
        }

        .font-bebas {
            font-family: '<PERSON><PERSON> Neue', cursive;
        }

        .poster-container {
            max-width: 800px; /* Reduced width for screenshot */
            margin: 1rem auto;
            background: linear-gradient(135deg, #1e293b, #0f172a);
            border-radius: 16px;
            box-shadow: 
                0 15px 30px -5px rgba(0, 0, 0, 0.6),
                0 0 0 2px rgba(76, 175, 80, 0.4),
                inset 0 0 20px rgba(76, 175, 80, 0.1);
            overflow: hidden;
            color: #f8fafc;
            position: relative;
        }

        /* Stylish diagonal line accent */
        .poster-container::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
            background: 
                linear-gradient(135deg, transparent 0%, transparent 49.9%, 
                rgba(76, 175, 80, 0.2) 50%, transparent 50.1%, transparent 100%);
            pointer-events: none;
            z-index: 1;
        }

        .player-card {
            background-color: rgba(30, 41, 59, 0.7);
            border: 1px solid rgba(76, 175, 80, 0.3);
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            position: relative;
            overflow: hidden;
        }

        .player-card::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, transparent, #4ade80, transparent);
            transform: scaleX(0);
            transition: transform 0.4s ease;
        }

        .player-card:hover {
            transform: translateY(-5px); /* Reduced movement */
            box-shadow: 
                0 10px 15px -3px rgba(0, 0, 0, 0.5),
                0 0 10px rgba(76, 175, 80, 0.4);
            border-color: rgba(76, 175, 80, 0.6);
        }

        .player-card:hover::after {
            transform: scaleX(1);
        }

        .player-card img {
            aspect-ratio: 3/4;
            object-fit: cover;
            transition: transform 0.5s ease;
            border-bottom: 2px solid rgba(76, 175, 80, 0.3);
        }

        .player-card:hover img {
            transform: scale(1.05);
        }

        /* Team logo styling */
        .team-logo-img {
            width: 50px; /* Smaller logo */
            height: 50px;
            object-fit: contain;
            border-radius: 8px;
            background-color: rgba(255, 255, 255, 0.97);
            padding: 6px;
            box-shadow: 
                0 5px 10px -3px rgba(0, 0, 0, 0.5),
                0 0 0 1px rgba(76, 175, 80, 0.6);
            transition: all 0.3s ease;
        }

        /* Header styling */
        .header {
            display: flex;
            flex-direction: column;
            align-items: center;
            position: relative;
            padding-top: 1.5rem;
        }

        .logo-container {
            margin-bottom: 1rem;
        }

        .team-logo-img:hover {
            transform: scale(1.1) rotate(5deg);
        }

        .header-title {
            background: linear-gradient(90deg, #4ade80, #22c55e);
            background-clip: text;
            -webkit-background-clip: text;
            color: transparent;
            -webkit-text-fill-color: transparent;
            text-shadow: 0 0 20px rgba(74, 222, 128, 0.3);
            letter-spacing: 0.05em;
            position: relative;
            display: inline-block;
        }

        /* Subtitle styling */
        .subtitle {
            position: relative;
            display: inline-block;
            overflow: hidden;
            padding: 0.2em 1em;
            background: rgba(76, 175, 80, 0.15);
            border-radius: 4px;
        }

        /* Style for player names */
        .player-name {
            position: relative;
            display: inline-block;
            transition: transform 0.3s ease;
        }

        .player-name::after {
            content: '';
            position: absolute;
            width: 100%;
            height: 2px;
            bottom: -2px;
            left: 0;
            background-color: #4ade80;
            transform: scaleX(0);
            transform-origin: right;
            transition: transform 0.3s ease;
        }

        .player-card:hover .player-name::after {
            transform: scaleX(1);
            transform-origin: left;
        }

        /* User instructions */
        .user-instructions {
            background-color: #1e293b;
            color: #e2e8f0;
            padding: 15px;
            margin: 15px auto;
            max-width: 800px;
            border: 1px solid #334155;
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            font-size: 0.9rem;
        }

        .user-instructions strong {
            color: #4ade80;
        }

        /* Footer styling */
        .poster-footer {
            background: linear-gradient(0deg, rgba(76, 175, 80, 0.1), transparent);
            padding: 1rem;
            border-top: 1px solid rgba(76, 175, 80, 0.3);
        }

        /* Special animation for hashtags */
        .hashtags {
            display: inline-block;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 0.7; }
            50% { opacity: 1; }
            100% { opacity: 0.7; }
        }

        /* Responsive adjustments for screenshot size */
        @media (max-width: 768px) {
            .poster-container {
                max-width: 600px;
            }
        }
    </style>
</head>
<body class="bg-slate-900">

    <div class="user-instructions">
        <p><strong>Instructions:</strong></p>
        <ol class="list-decimal list-inside text-left max-w-md mx-auto">
            <li>Create a folder named <strong>midfield</strong> in the same directory as this HTML file.</li>
            <li>Place your NNUFC logo as <strong>logo.svg</strong> (preferred) or <strong>logo.png</strong> in the <strong>midfield</strong> folder.</li>
            <li>Place your 5 midfielder photos in that <strong>midfield</strong> folder.</li>
            <li>Name them: <strong>player1.jpg</strong>, <strong>player2.jpg</strong>, ..., <strong>player5.jpg</strong>.</li>
            <li>Update player names below if needed.</li>
            <li>Open this HTML file in your browser and take a screenshot for social media!</li>
        </ol>
    </div>

    <div class="poster-container">
        <header class="header p-5 md:p-6 text-center">
            <div class="logo-container">
                <img src="midfield/logo.svg"
                     alt="NNUFC Team Logo"
                     class="team-logo-img mx-auto"
                     onerror="this.onerror=null; this.src='midfield/logo.png'; this.onerror=function(){this.onerror=null; this.src='https://placehold.co/75x75/4CAF50/FFFFFF?text=NNUFC&font=montserrat';};">
            </div>
            <h1 class="font-bebas text-4xl md:text-5xl lg:text-6xl tracking-wider header-title mb-2">
                NNUFC'S MIDFIELD ENGINE
            </h1>
            <p class="font-bebas text-xl md:text-2xl text-green-400 mt-2 tracking-wide subtitle">THE PLAYMAKER SQUAD</p>
        </header>

        <main class="p-4 md:p-6">
            <!-- Compact grid layout for 5 players -->
            <div class="grid grid-cols-2 md:grid-cols-3 gap-3 md:gap-4">
                <!-- Player 1: Sujan Singh -->
                <div class="player-card rounded-xl shadow-lg overflow-hidden">
                    <img src="midfield/player1.jpg"
                         onerror="this.onerror=null; this.src='https://placehold.co/300x400/1e293b/f8fafc?text=Sujan+Singh&font=montserrat';"
                         alt="Sujan Singh" class="w-full">
                    <div class="p-2 text-center bg-gradient-to-t from-slate-900/70 to-transparent">
                        <h2 class="text-base md:text-lg font-bold text-green-300 player-name">Sujan Singh</h2>
                        <p class="text-xs md:text-sm text-gray-300">Midfield / 8</p>
                    </div>
                </div>

                <!-- Player 2: Saroj Budhathoki -->
                <div class="player-card rounded-xl shadow-lg overflow-hidden">
                    <img src="midfield/player2.jpg"
                         onerror="this.onerror=null; this.src='https://placehold.co/300x400/1e293b/f8fafc?text=Saroj+Budhathoki&font=montserrat';"
                         alt="Saroj Budhathoki" class="w-full">
                    <div class="p-2 text-center bg-gradient-to-t from-slate-900/70 to-transparent">
                        <h2 class="text-base md:text-lg font-bold text-green-300 player-name">Saroj Budhathoki</h2>
                        <p class="text-xs md:text-sm text-gray-300">Attacking midfield / 9</p>
                    </div>
                </div>

                <!-- Player 3: Nishan Magar -->
                <div class="player-card rounded-xl shadow-lg overflow-hidden">
                    <img src="midfield/player3.jpg"
                         onerror="this.onerror=null; this.src='https://placehold.co/300x400/1e293b/f8fafc?text=Nishan+Magar&font=montserrat';"
                         alt="Nishan Magar" class="w-full">
                    <div class="p-2 text-center bg-gradient-to-t from-slate-900/70 to-transparent">
                        <h2 class="text-base md:text-lg font-bold text-green-300 player-name">Nishan Magar</h2>
                        <p class="text-xs md:text-sm text-gray-300">Midfield / -</p>
                    </div>
                </div>

                <!-- Player 4: Sunil Rai -->
                <div class="player-card rounded-xl shadow-lg overflow-hidden md:col-start-1 md:col-end-2">
                    <img src="midfield/player4.jpg"
                         onerror="this.onerror=null; this.src='https://placehold.co/300x400/1e293b/f8fafc?text=Sunil+Rai&font=montserrat';"
                         alt="Sunil Rai" class="w-full">
                    <div class="p-2 text-center bg-gradient-to-t from-slate-900/70 to-transparent">
                        <h2 class="text-base md:text-lg font-bold text-green-300 player-name">Sunil Rai</h2>
                        <p class="text-xs md:text-sm text-gray-300">Midfield / 87</p>
                    </div>
                </div>

                <!-- Player 5: Rojan Pyakurel -->
                <div class="player-card rounded-xl shadow-lg overflow-hidden md:col-start-2 md:col-end-3">
                    <img src="midfield/player5.jpg"
                         onerror="this.onerror=null; this.src='https://placehold.co/300x400/1e293b/f8fafc?text=Rojan+Pyakurel&font=montserrat';"
                         alt="Rojan Pyakurel" class="w-full">
                    <div class="p-2 text-center bg-gradient-to-t from-slate-900/70 to-transparent">
                        <h2 class="text-base md:text-lg font-bold text-green-300 player-name">Rojan Pyakurel</h2>
                        <p class="text-xs md:text-sm text-gray-300">Midfield / 6</p>
                    </div>
                </div>
            </div>
        </main>

        <footer class="poster-footer text-center mt-3">
            <p class="text-xl md:text-2xl font-bold text-green-400 font-bebas tracking-wider">NNUFC</p>
            <p class="text-xs text-gray-300 mt-1"><span id="currentYear">2025</span></p>
            <p class="text-xs text-green-500 mt-2 uppercase tracking-wider hashtags">#MidfieldMaestros #Playmakers</p>
        </footer>
    </div>

    <script>
        // Set current year in footer
        document.getElementById('currentYear').textContent = new Date().getFullYear();
        
        // Add subtle parallax effect to player cards on mouse move
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.player-card');
            
            document.addEventListener('mousemove', function(e) {
                const x = e.clientX / window.innerWidth - 0.5;
                const y = e.clientY / window.innerHeight - 0.5;
                
                cards.forEach(card => {
                    const intensity = 10; // Reduced intensity
                    card.style.transform = `perspective(1000px) rotateY(${x * intensity}deg) rotateX(${-y * intensity}deg) translateZ(5px)`;
                });
            });
            
            // Reset transform when mouse leaves the page
            document.addEventListener('mouseleave', function() {
                cards.forEach(card => {
                    card.style.transform = 'perspective(1000px) rotateY(0) rotateX(0) translateZ(0)';
                });
            });
        });
    </script>

</body>
</html>