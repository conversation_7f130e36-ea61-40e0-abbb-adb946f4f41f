.<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Our Club Defenders - Poster</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Bebas+Neue&family=Montserrat:wght@400;600;700&display=swap" rel="stylesheet">

    <style>
        /* Custom Styles */
        body {
            font-family: 'Montserrat', sans-serif; /* Default body font */
            background-color: #1a202c; /* Dark background for the page */
        }

        .font-bebas {
            font-family: 'Bebas Neue', cursive; /* Title font */
        }

        .poster-container {
            max-width: 900px; /* Adjust for desired poster width */
            margin: 2rem auto;
            background: linear-gradient(145deg, #2d3748, #1a202c); /* Dark gradient for poster bg */
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5), 0 0 0 2px rgba(76, 175, 80, 0.6); /* Shadow and subtle team color border */
            overflow: hidden; /* Ensures rounded corners clip content */
            color: #e2e8f0; /* Light text color for contrast */
        }

        .player-card {
            background-color: rgba(255, 255, 255, 0.05); /* Slightly transparent card background */
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: transform 0.3s ease-in-out, box-shadow 0.3s ease-in-out;
        }

        .player-card:hover {
            transform: translateY(-8px) scale(1.03);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.4);
        }

        .player-card img {
            aspect-ratio: 3/4; /* Portrait aspect ratio for player defender */
            object-fit: cover; /* Ensures defender cover the area, might crop */
            border-bottom: 2px solid #4A5568; /* Separator line */
        }

        /* Styling for the team logo image */
        .team-logo-img {
            width: 50px;  /* Smaller on mobile */
            height: 50px;
            object-fit: contain; /* Maintains aspect ratio without distortion */
            border-radius: 8px;
            background-color: rgba(255, 255, 255, 0.95); /* White background for better contrast */
            padding: 6px; /* Padding inside the logo container */
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4); /* Enhanced shadow for depth */
            transition: transform 0.3s ease; /* Smooth hover effect */
            display: block; /* Ensure proper block display */
        }

        /* Logo container for better positioning */
        .logo-container {
            position: absolute;
            top: 16px;
            left: 16px;
            z-index: 20;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* Responsive logo sizing and positioning */
        @media (min-width: 768px) {
            .team-logo-img {
                width: 65px;  /* Medium screens */
                height: 65px;
                padding: 8px;
            }
            .logo-container {
                top: 24px;
                left: 24px;
            }
        }

        @media (min-width: 1024px) {
            .team-logo-img {
                width: 75px;  /* Large screens */
                height: 75px;
                padding: 10px;
            }
            .logo-container {
                top: 32px;
                left: 32px;
            }
        }

        /* Logo hover effect */
        .team-logo-img:hover {
            transform: scale(1.08);
            box-shadow: 0 6px 16px rgba(0, 0, 0, 0.5);
        }

        .header-title {
            background: linear-gradient(#68D391, #38A169); /* Green gradient for title */
            background: -webkit-linear-gradient(#68D391, #38A169); /* Webkit fallback */
            background-clip: text;
            -webkit-background-clip: text;
            color: transparent;
            -webkit-text-fill-color: transparent;
        }

        /* Instructions for the user - hidden on the poster itself */
        .user-instructions {
            background-color: #fff3cd;
            color: #856404;
            padding: 15px;
            margin: 20px auto;
            max-width: 900px;
            border: 1px solid #ffeeba;
            border-radius: 8px;
            text-align: center;
        }
    </style>
</head>
<body>

    <div class="user-instructions">
        <p><strong>Instructions:</strong></p>
        <ol class="list-decimal list-inside text-left max-w-md mx-auto">
            <li>Create a folder named `<strong>defender</strong>` in the same directory as this HTML file.</li>
            <li>Place your NNUFC logo as `logo.svg` (preferred) or `logo.png` in the `defender` folder.</li>
            <li>Place your 8 defender photos in that `defender` folder.</li>
            <li>Name them: `defender_1.jpg`, `defender_2.jpg`, ..., `defender_8.jpg`.</li>
            <li>Update player names below.</li>
            <li>Open this HTML file in your browser and take a screenshot for social media!</li>
        </ol>
    </div>

    <div class="poster-container">
        <header class="p-6 md:p-10 text-center relative">
            <div class="logo-container">
                <!-- Try SVG first, fallback to PNG, then placeholder -->
                <img src="defender/logo.svg"
                     alt="NNUFC Team Logo"
                     class="team-logo-img"
                     onerror="this.onerror=null; this.src='defender/logo.png'; this.onerror=function(){this.onerror=null; this.src='https://placehold.co/75x75/4CAF50/FFFFFF?text=NNUFC&font=montserrat';};">
            </div>
            <h1 class="font-bebas text-4xl md:text-6xl lg:text-7xl tracking-wider header-title">
                NNUFC'S DEFENSIVE WALL
            </h1>
            <p class="font-bebas text-2xl md:text-3xl text-green-400 mt-1 tracking-wide">THE UNBREAKABLE 8</p>
        </header>

        <main class="p-6 md:p-8">
            <div class="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 md:gap-6">

                <!-- Left Backs -->
                <div class="player-card rounded-xl shadow-lg overflow-hidden">
                    <img src="defender/defender_1.jpg"
                         onerror="this.onerror=null; this.src='https://placehold.co/300x400/4A5568/E2E8F0?text=Defender+1&font=montserrat';"
                         alt="ISAGI" class="w-full">
                    <div class="p-3 md:p-4 text-center">
                        <h2 class="text-lg md:text-xl font-semibold text-green-300">ISAGI</h2>
                        <p class="text-xs md:text-sm text-gray-400">LB / 2</p>
                    </div>
                </div>

                <div class="player-card rounded-xl shadow-lg overflow-hidden">
                    <img src="defender/defender_7.jpg"
                         onerror="this.onerror=null; this.src='https://placehold.co/300x400/4A5568/E2E8F0?text=Defender+7&font=montserrat';"
                         alt="Dipen Sunuwar" class="w-full">
                    <div class="p-3 md:p-4 text-center">
                        <h2 class="text-lg md:text-xl font-semibold text-green-300">Dipen Sunuwar</h2>
                        <p class="text-xs md:text-sm text-gray-400">LB/RB / 75</p>
                    </div>
                </div>

                <div class="player-card rounded-xl shadow-lg overflow-hidden">
                    <img src="defender/defender_2.jpg"
                         onerror="this.onerror=null; this.src='https://placehold.co/300x400/4A5568/E2E8F0?text=Defender+2&font=montserrat';"
                         alt="Prabin Shrestha" class="w-full">
                    <div class="p-3 md:p-4 text-center">
                        <h2 class="text-lg md:text-xl font-semibold text-green-300">Prabin Shrestha</h2>
                        <p class="text-xs md:text-sm text-gray-400">LB/RB / 98</p>
                    </div>
                </div>

                <!-- Center Backs -->
                <div class="player-card rounded-xl shadow-lg overflow-hidden">
                    <img src="defender/defender_4.jpg"
                         onerror="this.onerror=null; this.src='https://placehold.co/300x400/4A5568/E2E8F0?text=Defender+4&font=montserrat';"
                         alt="Ashis Khadka" class="w-full">
                    <div class="p-3 md:p-4 text-center">
                        <h2 class="text-lg md:text-xl font-semibold text-green-300">Ashis Khadka</h2>
                        <p class="text-xs md:text-sm text-gray-400">CB / 24</p>
                    </div>
                </div>

                <div class="player-card rounded-xl shadow-lg overflow-hidden">
                    <img src="defender/defender_8.jpg"
                         onerror="this.onerror=null; this.src='https://placehold.co/300x400/4A5568/E2E8F0?text=Defender+8&font=montserrat';"
                         alt="Shivam Rawal" class="w-full">
                    <div class="p-3 md:p-4 text-center">
                        <h2 class="text-lg md:text-xl font-semibold text-green-300">Shivam Rawal</h2>
                        <p class="text-xs md:text-sm text-gray-400">CB / 7</p>
                    </div>
                </div>

                <!-- Right Backs -->
                <div class="player-card rounded-xl shadow-lg overflow-hidden">
                    <img src="defender/defender_3.jpg"
                         onerror="this.onerror=null; this.src='https://placehold.co/300x400/4A5568/E2E8F0?text=Defender+3&font=montserrat';"
                         alt="Anish Thapa" class="w-full">
                    <div class="p-3 md:p-4 text-center">
                        <h2 class="text-lg md:text-xl font-semibold text-green-300">Anish Thapa</h2>
                        <p class="text-xs md:text-sm text-gray-400">RB / 5</p>
                    </div>
                </div>

                <div class="player-card rounded-xl shadow-lg overflow-hidden">
                    <img src="defender/defender_5.jpg"
                         onerror="this.onerror=null; this.src='https://placehold.co/300x400/4A5568/E2E8F0?text=Defender+5&font=montserrat';"
                         alt="Niraj Gurung" class="w-full">
                    <div class="p-3 md:p-4 text-center">
                        <h2 class="text-lg md:text-xl font-semibold text-green-300">Niraj Gurung</h2>
                        <p class="text-xs md:text-sm text-gray-400">RB / 23</p>
                    </div>
                </div>

                <div class="player-card rounded-xl shadow-lg overflow-hidden">
                    <img src="defender/defender_6.jpg"
                         onerror="this.onerror=null; this.src='https://placehold.co/300x400/4A5568/E2E8F0?text=Defender+6&font=montserrat';"
                         alt="Sangam Lamsal" class="w-full">
                    <div class="p-3 md:p-4 text-center">
                        <h2 class="text-lg md:text-xl font-semibold text-green-300">Sangam Lamsal</h2>
                        <p class="text-xs md:text-sm text-gray-400">RB / 17</p>
                    </div>
                </div>
            </div>
        </main>

        <footer class="p-6 text-center border-t border-gray-700 mt-4">
            <p class="text-lg md:text-xl font-bold text-green-400 font-bebas tracking-wider">NNUFC</p>
            <p class="text-sm text-gray-400 mt-1"><span id="currentYear"></span></p>
            <p class="text-xs text-green-500 mt-2 uppercase tracking-wider">#FearTheWall #offering a thriving platform for enthusiasts to play</p>
        </footer>
    </div>

    <script>
        // Simple JS to set the current year in the footer
        document.getElementById('currentYear').textContent = new Date().getFullYear();
    </script>

</body>
</html>
